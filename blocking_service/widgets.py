from django.forms.widgets import SelectMultiple
from django.utils.html import format_html
from django.utils.safestring import mark_safe
from django.forms.utils import flatatt


class AjaxSelect2MultipleWidget(SelectMultiple):
    """
    Custom widget for Select2 with AJAX functionality
    """
    
    def __init__(self, ajax_url=None, attrs=None):
        self.ajax_url = ajax_url
        super(AjaxSelect2MultipleWidget, self).__init__(attrs)

    def render(self, name, value, attrs=None):
        if value is None:
            value = []
        
        final_attrs = self.build_attrs(attrs, name=name)
        final_attrs['class'] = final_attrs.get('class', '') + ' select2_ajax_multiple'
        final_attrs['data-ajax-url'] = self.ajax_url
        
        output = [format_html('<select multiple="multiple"{}>', flatatt(final_attrs))]
        
        # Render selected options if any
        if value:
            from blocking_service.models import Numbers
            selected_numbers = Numbers.objects.filter(id__in=value)
            for number in selected_numbers:
                output.append(format_html(
                    '<option value="{}" selected="selected">{}</option>',
                    number.id,
                    number.phone_number
                ))
        
        output.append('</select>')
        return mark_safe('\n'.join(output))
