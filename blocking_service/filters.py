import datetime

import django_filters as filters
from django.db.models import Q, Count

from core.widgets import DateRangeWidget, DatePickerWidget, MultiSelect
from roles.models import User
from . import models
from .models import SCENARIO_BLOCK, SOURCE, SCENARIO_UNBLOCK_PERSON, \
    SCENARIO_UNBLOCK_LEGAL, PrescriptionStatus, PrescriptionHistoryStatus, \
    PrescriptionType, Numbers, BlockStatus, NumbersPrescription


class BlockListFilter(filters.FilterSet):
    ud_number = filters.NumberFilter(
        name='ud_number',
        label='№ УД (материала проверки)',
    )
    registration_number = filters.NumberFilter(
        name='registration_number',
        label='Регистрационный №',
    )
    id = filters.NumberFilter(
        name='id',
        label='Уникальный идентификатор',
    )
    scenario = filters.ChoiceFilter(
        name='scenario',
        label='Вид применяемых ограничений',
        choices=SCENARIO_BLOCK,
    )
    source = filters.ChoiceFilter(
        name='source',
        label='Источник',
        choices=SOURCE,
    )
    source_structural_unit = filters.CharFilter(
        name='source_structural_unit',
        label='Наименования структурного подразделения органа, выдавшего представление',
    )
    _date_created = filters.DateFromToRangeFilter(
        name='_date_created',
        label='Дата создания',
        widget=DateRangeWidget,
    )
    date_complete = filters.DateFromToRangeFilter(
        name='date_complete',
        label='Дата исполнения',
        widget=DateRangeWidget,
    )
    status = filters.ChoiceFilter(
        name='status',
        label='Статус',
        choices=PrescriptionStatus.humanize_choices(),
    )
    author = filters.ModelChoiceFilter(
        name='author',
        label='Исполнитель',
        queryset=User.objects.all(),
    )
    block_number_list = filters.ModelMultipleChoiceFilter(
        name='block_number_list',
        label='Абонентский номер (номера), подлежащий блокировке',
        queryset=Numbers.objects.filter(number_prescription_through__block_status=BlockStatus.BLOCKED.value).distinct(),
        method='filter_block_number_list',
        widget=MultiSelect,
    )

    @staticmethod
    def filter_block_number_list(queryset, name, value):
        number_prescription_list = NumbersPrescription.objects.filter(
            number__in=value,
            block_status=BlockStatus.BLOCKED.value
        ).values('prescription_id').annotate(
            matching_numbers_count=Count('number')
        ).filter(matching_numbers_count__gte=len(value))

        return queryset.filter(id__in=[number_prescription['prescription_id'] for number_prescription in number_prescription_list])

    class Meta:
        model = models.Prescription
        fields = [
            'ud_number',
            'registration_number',
            'id',
            'scenario',
            'source',
            'source_structural_unit',
            '_date_created',
            'date_complete',
            'status',
            'author',
            'block_number_list',
        ]


class UnblockPersonListFilter(filters.FilterSet):
    registration_number = filters.NumberFilter(
        name='registration_number',
        label='Регистрационный №',
    )
    id = filters.NumberFilter(
        name='id',
        label='Уникальный идентификатор',
    )
    full_name = filters.CharFilter(
        name='full_name',
        label='Ф.И.О. абонента',
        method='filter_full_name',
    )
    birth_date = filters.DateFilter(
        name='birth_date',
        label='Дата рождения',
        widget=DatePickerWidget,
        method='filter_birth_date',
    )
    document_number = filters.CharFilter(
        name='document_number',
        label='Идентификационный номер (номер документа, удостоверяющего личность)',
        method='filter_document_number',
    )
    scenario = filters.ChoiceFilter(
        name='scenario',
        label='Вид возобновления услуг',
        choices=SCENARIO_UNBLOCK_PERSON,
    )
    source = filters.ChoiceFilter(
        name='source',
        label='Источник',
        choices=SOURCE,
    )
    source_structural_unit = filters.CharFilter(
        name='source_structural_unit',
        label='Наименования структурного подразделения органа, выдавшего представление',
    )
    _date_created = filters.DateFromToRangeFilter(
        name='_date_created',
        label='Дата создания',
        widget=DateRangeWidget,
    )
    date_complete = filters.DateFromToRangeFilter(
        name='date_complete',
        label='Дата исполнения',
        widget=DateRangeWidget,
    )
    status = filters.ChoiceFilter(
        name='status',
        label='Статус',
        choices=PrescriptionStatus.humanize_choices(),
    )
    author = filters.ModelChoiceFilter(
        name='author',
        label='Исполнитель',
        queryset=User.objects.all(),
    )

    @staticmethod
    def filter_full_name(queryset, name, value):
        parts = value.split()
        query = Q()
        if len(parts) >= 1:
            query &= Q(extra__surname__contains=parts[0])
        if len(parts) >= 2:
            query &= Q(extra__name__contains=parts[1])
        if len(parts) >= 3:
            query &= Q(extra__patronymic__contains=parts[2])

        return queryset.filter(query)

    @staticmethod
    def filter_birth_date(queryset, name, value):
        return queryset.filter(extra__birth_date=value.strftime("%Y-%m-%d"))

    @staticmethod
    def filter_document_number(queryset, name, value):
        return queryset.filter(extra__document_number=value)

    class Meta:
        model = models.Prescription
        fields = [
            'registration_number',
            'id',
            'full_name',
            'birth_date',
            'document_number',
            'scenario',
            'source',
            'source_structural_unit',
            '_date_created',
            'date_complete',
            'status',
            'author',
        ]


class UnblockLegalListFilter(filters.FilterSet):
    registration_number = filters.NumberFilter(
        name='registration_number',
        label='Регистрационный №',
    )
    id = filters.NumberFilter(
        name='id',
        label='Уникальный идентификатор',
    )
    name = filters.CharFilter(
        name='name',
        label='Наименование организации',
        method='filter_name',
    )
    unp = filters.CharFilter(
        name='unp',
        label='УНП',
        method='filter_unp',
    )
    scenario = filters.ChoiceFilter(
        name='scenario',
        label='Вид возобновления услуг',
        choices=SCENARIO_UNBLOCK_LEGAL,
    )
    source = filters.ChoiceFilter(
        name='source',
        label='Источник',
        choices=SOURCE,
    )
    source_structural_unit = filters.CharFilter(
        name='source_structural_unit',
        label='Наименования структурного подразделения органа, выдавшего представление',
    )
    _date_created = filters.DateFromToRangeFilter(
        name='_date_created',
        label='Дата создания',
        widget=DateRangeWidget,
    )
    date_complete = filters.DateFromToRangeFilter(
        name='date_complete',
        label='Дата исполнения',
        widget=DateRangeWidget,
    )
    status = filters.ChoiceFilter(
        name='status',
        label='Статус',
        choices=PrescriptionStatus.humanize_choices(),
    )
    author = filters.ModelChoiceFilter(
        name='author',
        label='Исполнитель',
        queryset=User.objects.all(),
    )

    @staticmethod
    def filter_name(queryset, name, value):
        return queryset.filter(extra__legal_name=value)

    @staticmethod
    def filter_unp(queryset, name, value):
        return queryset.filter(extra__unp=value)

    class Meta:
        model = models.Prescription
        fields = [
            'registration_number',
            'id',
            'name',
            'unp',
            'scenario',
            'source',
            'source_structural_unit',
            '_date_created',
            'date_complete',
            'status',
            'author',
        ]


class PersonListFilter(filters.FilterSet):
    full_name = filters.CharFilter(
        label='Ф.И.О. абонента',
        method='filter_full_name',
    )
    birth_date = filters.DateFilter(
        name='object_of_prescription__person__birth_date',
        label='Дата рождения',
        widget=DatePickerWidget,
    )
    document_number = filters.CharFilter(
        name='object_of_prescription__person__document_number',
        label='Личный номер (номер паспорта)',
    )
    history_status = filters.ChoiceFilter(
        label='Статус',
        choices=PrescriptionHistoryStatus.choices(),
        method='filter_history_status',
    )
    block_author = filters.ModelChoiceFilter(
        label='УЛ, внесшее представление',
        method='filter_block_author',
        queryset=User.objects.all(),
    )
    block_registration_number = filters.NumberFilter(
        label='Рег. № представления',
        method='filter_block_registration_number'
    )
    block_ud_number = filters.NumberFilter(
        label='№ УД (материала проверки)',
        method='filter_block_ud_number'
    )
    block_date_created = filters.DateFilter(
        label='Дата ввода ограничений',
        method='filter_block_date_created',
        widget=DatePickerWidget,
    )
    block_scenario = filters.ChoiceFilter(
        label='Тип ограничений',
        method='filter_block_scenario',
        choices=SCENARIO_BLOCK,
    )
    unblock_author = filters.ModelChoiceFilter(
        label='УЛ, внесшее решение',
        method='filter_unblock_author',
        queryset=User.objects.all(),
    )
    unblock_registration_number = filters.NumberFilter(
        label='Рег. № решения',
        method='filter_unblock_registration_number'
    )
    restrictions_end_date = filters.DateFilter(
        label='Дата снятия ограничений',
        method='filter_restrictions_end_date',
        widget=DatePickerWidget,
    )
    unblock_scenario = filters.ChoiceFilter(
        label='Тип разрешений',
        method='filter_unblock_scenario',
        choices=SCENARIO_UNBLOCK_PERSON,
    )

    @staticmethod
    def filter_full_name(queryset, name, value):
        parts = value.split()

        query = Q()
        if len(parts) >= 1:
            query &= Q(object_of_prescription__person__surname__icontains=parts[0])
        if len(parts) >= 2:
            query &= Q(object_of_prescription__person__name__icontains=parts[1])
        if len(parts) >= 3:
            query &= Q(object_of_prescription__person__patronymic__icontains=parts[2])

        return queryset.filter(query)

    @staticmethod
    def filter_history_status(queryset, name, value):
        query = Q(prescription__scenario__in=[2, 3, 5])
        if int(value) != PrescriptionHistoryStatus.BLOCKED.value:
            query = ~query
        return queryset.filter(query)

    @staticmethod
    def filter_block_author(queryset, name, value):
        return queryset.filter(
            prescription__author=value,
            prescription__prescription_type=PrescriptionType.BLOCK.value
        )

    @staticmethod
    def filter_block_registration_number(queryset, name, value):
        return queryset.filter(
            prescription__registration_number=value,
            prescription__prescription_type=PrescriptionType.BLOCK.value
        )

    @staticmethod
    def filter_block_ud_number(queryset, name, value):
        return queryset.filter(
            prescription__ud_number=value,
            prescription__prescription_type=PrescriptionType.BLOCK.value
        )

    @staticmethod
    def filter_block_date_created(queryset, name, value):
        return queryset.filter(
            prescription___date_created__date=value,
            prescription__prescription_type=PrescriptionType.BLOCK.value
        )

    @staticmethod
    def filter_block_scenario(queryset, name, value):
        return queryset.filter(
            prescription__scenario=value,
            prescription__prescription_type=PrescriptionType.BLOCK.value
        )

    @staticmethod
    def filter_unblock_author(queryset, name, value):
        return queryset.filter(
            prescription__author=value,
            prescription__prescription_type=PrescriptionType.UNBLOCK_PERSON.value
        )

    @staticmethod
    def filter_unblock_registration_number(queryset, name, value):
        return queryset.filter(
            prescription__registration_number=value,
            prescription__prescription_type=PrescriptionType.UNBLOCK_PERSON.value
        )

    @staticmethod
    def filter_restrictions_end_date(queryset, name, value):
        query = Q(
            prescription__date_complete__isnull=False,
        )
        return queryset.filter(
            query & (
                Q(
                    prescription__prescription_type=PrescriptionType.UNBLOCK_PERSON.value,
                    prescription__date_complete__date=value
                ) | Q(
                    prescription__prescription_type=PrescriptionType.BLOCK.value,
                    prescription__date_complete__date=value + datetime.timedelta(days=90)
                )
            )
        )

    @staticmethod
    def filter_unblock_scenario(queryset, name, value):
        return queryset.filter(
            prescription__scenario=value,
            prescription__prescription_type=PrescriptionType.UNBLOCK_PERSON.value
        )

    class Meta:
        model = models.ObjectPrescription
        fields = [
            'full_name',
            'birth_date',
            'document_number',
            'history_status',
            'block_author',
            'block_registration_number',
            'block_ud_number',
            'block_date_created',
            'block_scenario',
            'unblock_author',
            'unblock_registration_number',
            'restrictions_end_date',
            'unblock_scenario',
        ]


class LegalListFilter(filters.FilterSet):
    name = filters.CharFilter(
        name='object_of_prescription__legal_entity__name',
        label='Наименование организации',
        lookup_expr='icontains',
    )
    unp = filters.CharFilter(
        name='object_of_prescription__legal_entity__unp',
        label='УНП',
    )
    history_status = filters.ChoiceFilter(
        label='Статус',
        choices=PrescriptionHistoryStatus.choices(),
        method='filter_history_status',
    )
    block_author = filters.ModelChoiceFilter(
        label='УЛ, внесшее представление',
        method='filter_block_author',
        queryset=User.objects.all(),
    )
    block_registration_number = filters.NumberFilter(
        label='Рег. № представления',
        method='filter_block_registration_number'
    )
    block_ud_number = filters.NumberFilter(
        label='№ УД (материала проверки)',
        method='filter_block_ud_number'
    )
    block_date_created = filters.DateFilter(
        label='Дата ввода ограничений',
        method='filter_block_date_created',
        widget=DatePickerWidget,
    )
    block_scenario = filters.ChoiceFilter(
        label='Тип ограничений',
        method='filter_block_scenario',
        choices=SCENARIO_BLOCK,
    )
    unblock_author = filters.ModelChoiceFilter(
        label='УЛ, внесшее решение',
        method='filter_unblock_author',
        queryset=User.objects.all(),
    )
    unblock_registration_number = filters.NumberFilter(
        label='Рег. № решения',
        method='filter_unblock_registration_number'
    )
    restrictions_end_date = filters.DateFilter(
        label='Дата снятия ограничений',
        method='filter_restrictions_end_date',
        widget=DatePickerWidget,
    )
    unblock_scenario = filters.ChoiceFilter(
        label='Тип разрешений',
        method='filter_unblock_scenario',
        choices=SCENARIO_UNBLOCK_LEGAL,
    )

    @staticmethod
    def filter_history_status(queryset, name, value):
        query = Q(prescription__scenario__in=[2, 3, 5])
        if int(value) != PrescriptionHistoryStatus.BLOCKED.value:
            query = ~query
        return queryset.filter(query)

    @staticmethod
    def filter_block_author(queryset, name, value):
        return queryset.filter(
            prescription__author=value,
            prescription__prescription_type=PrescriptionType.BLOCK.value
        )

    @staticmethod
    def filter_block_registration_number(queryset, name, value):
        return queryset.filter(
            prescription__registration_number=value,
            prescription__prescription_type=PrescriptionType.BLOCK.value
        )

    @staticmethod
    def filter_block_ud_number(queryset, name, value):
        return queryset.filter(
            prescription__ud_number=value,
            prescription__prescription_type=PrescriptionType.BLOCK.value
        )

    @staticmethod
    def filter_block_date_created(queryset, name, value):
        return queryset.filter(
            prescription___date_created__date=value,
            prescription__prescription_type=PrescriptionType.BLOCK.value
        )

    @staticmethod
    def filter_block_scenario(queryset, name, value):
        return queryset.filter(
            prescription__scenario=value,
            prescription__prescription_type=PrescriptionType.BLOCK.value
        )

    @staticmethod
    def filter_unblock_author(queryset, name, value):
        return queryset.filter(
            prescription__author=value,
            prescription__prescription_type=PrescriptionType.UNBLOCK_LEGAL.value
        )

    @staticmethod
    def filter_unblock_registration_number(queryset, name, value):
        return queryset.filter(
            prescription__registration_number=value,
            prescription__prescription_type=PrescriptionType.UNBLOCK_LEGAL.value
        )

    @staticmethod
    def filter_restrictions_end_date(queryset, name, value):
        query = Q(
            prescription__date_complete__isnull=False,
        )
        return queryset.filter(
            query & (
                Q(
                    prescription__prescription_type=PrescriptionType.UNBLOCK_LEGAL.value,
                    prescription__date_complete__date=value
                ) | Q(
                    prescription__prescription_type=PrescriptionType.BLOCK.value,
                    prescription__date_complete__date=value + datetime.timedelta(days=90)
                )
            )
        )

    @staticmethod
    def filter_unblock_scenario(queryset, name, value):
        return queryset.filter(
            prescription__scenario=value,
            prescription__prescription_type=PrescriptionType.UNBLOCK_LEGAL.value
        )

    class Meta:
        model = models.LegalEntity
        fields = [
            'name',
            'unp',
            'history_status',
            'block_author',
            'block_registration_number',
            'block_ud_number',
            'block_date_created',
            'block_scenario',
            'unblock_author',
            'unblock_registration_number',
            'restrictions_end_date',
            'unblock_scenario',
        ]
